'use client'
import React from 'react'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/scrollbar'
import 'swiper/css/free-mode'
import Vessels from './overview-components/vessels/vessels'
import MaintenanceTasks from './overview-components/maintenance-tasks/maintenance-tasks'
import Training from './overview-components/training/training'
import DashboardCalendar from './overview-components/calendar'
import DashboardMap from './overview-components/map/map'
import RecentIncidents from './overview-components/recent-incidents/recent-incidents'
import { Card } from '@/components/ui/card'
export default function DashboardStatus(props: any) {
    return (
        <div className="w-full flex flex-col gap-8 overflow-hidden relative">
            <div className="grid grid-col-1 lg:grid-cols-3 gap-8 lg:gap-6 xl:gap-8">
                <Card>
                    <Vessels />
                </Card>
                <Card>
                    <MaintenanceTasks />
                </Card>
                <Card>
                    <Training />
                </Card>
            </div>
            <div className="w-full grid lg:grid-cols-3 gap-y-5 lg:gap-6 xl:gap-8 pb-10">
                <Card>
                    <RecentIncidents />
                </Card>
                <div>
                    <DashboardCalendar />
                </div>
                <Card className="h-full min-h-[400px]">
                    <DashboardMap />
                </Card>
            </div>
        </div>
    )
}
