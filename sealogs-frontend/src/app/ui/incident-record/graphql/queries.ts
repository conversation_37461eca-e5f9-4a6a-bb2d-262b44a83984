import gql from 'graphql-tag'

export const GET_INCIDENT_RECORD = gql`
    query GetIncidentRecord($id: ID!) {
        readOneIncidentRecord(filter: { id: { eq: $id } }) {
            id
            title
            startDate
            endDate
            incidentType
            personsInvolved
            description
            treatment
            contributingFactor
            riskAssessmentReviewed
            notifiable
            location {
                id
                title
                lat
                long
            }
            reportedBy {
                id
                firstName
                surname
            }
            vessel {
                id
                title
            }
            membersToNotify {
                nodes {
                    id
                    firstName
                    surname
                }
            }
            attachments {
                nodes {
                    id
                    title
                }
            }
        }
    }
`

export const GET_INCIDENT_RECORDS = gql`
    query GetIncidentRecords(
        $limit: Int = 100
        $offset: Int = 0
        $filter: IncidentRecordFilterFields = {}
    ) {
        readIncidentRecords(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { startDate: DESC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                title
                startDate
                endDate
                incidentType
                contributingFactor
                treatment
                vessel {
                    id
                    title
                }
                reportedBy {
                    id
                    firstName
                    surname
                }
                membersToNotify {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
            }
        }
    }
`
