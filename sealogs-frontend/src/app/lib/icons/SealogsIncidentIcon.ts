'use client'

import { useTheme } from "next-themes";

const React = require('react')

export function SealogsIncidentIcon(props: any) {
  const { theme, setTheme } = useTheme()

  const getTheme = () => {
    switch (theme) {
      case 'dark':
        return 'Dark'
      case 'light':
        return 'Light'
      default:
        return 'System'
    }
  }

  getTheme()

  // Use light version for both themes since health-dark.svg doesn't exist
  return React.createElement('img', {
    src: '/sealogs-V2_Icons/health-light.svg',
    alt: 'Incident Icon',
    ...props,
  });
}
