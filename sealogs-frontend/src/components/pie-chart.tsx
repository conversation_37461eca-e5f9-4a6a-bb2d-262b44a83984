'use client'
import { <PERSON>, <PERSON><PERSON><PERSON> } from 'recharts'
import {
    Chart<PERSON>ontainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart'
export function PieChartComponent({
    chartConfig,
    chartData,
}: {
    chartConfig: any
    chartData: any
    cardTitle?: string
}) {
    return (
        <div className="flex flex-col size-full border-0 !shadow-none min-h-0 !p-0 max-h-[300px]">
            <div className="flex flex-col min-h-0 size-full p-0">
                <ChartContainer
                    config={chartConfig}
                    className="mx-auto size-full aspect-square pb-0 [&_.recharts-pie-label-text]:fill-foreground">
                    <PieChart>
                        <ChartTooltip
                            content={
                                <ChartTooltipContent
                                    hideLabel
                                    hideIndicator
                                    className="chartToolTip"
                                />
                            }
                        />
                        <Pie
                            data={chartData}
                            dataKey="amount"
                            nameKey="title"
                            paddingAngle={5}
                        />
                    </PieChart>
                </ChartContainer>
            </div>
        </div>
    )
}
